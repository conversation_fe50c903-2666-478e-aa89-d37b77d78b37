<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9753156b-81ff-4777-afd9-c310252c21ea" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\maven\apache-maven-3.6.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="312L1YdJlQdZy3tqEoZ48nsXKdO" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.SpringbootApplication.executor": "Run",
    "last_opened_file_path": "E:/idea文件/项目一",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true",
    "应用程序.SpringbootApplication.executor": "Debug"
  }
}]]></component>
  <component name="RunManager" selected="应用程序.SpringbootApplication">
    <configuration name="SpringbootApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="qidian.it.springboot.SpringbootApplication" />
      <module name="springboot" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="qidian.it.springboot.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SpringbootApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="springboot" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="qidian.it.springboot.SpringbootApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="qidian.it.springboot.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.SpringbootApplication" />
        <item itemvalue="Spring Boot.SpringbootApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="9753156b-81ff-4777-afd9-c310252c21ea" name="更改" comment="" />
      <created>1754717005591</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754717005591</updated>
      <workItem from="1754717006752" duration="1287000" />
      <workItem from="1754719696153" duration="1826000" />
      <workItem from="1754723328455" duration="3380000" />
      <workItem from="1754813519464" duration="760000" />
      <workItem from="1755137779515" duration="26000" />
      <workItem from="1755157213111" duration="46000" />
      <workItem from="1755158436128" duration="164000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>