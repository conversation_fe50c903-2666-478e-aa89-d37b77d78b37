package qidian.it.springboot.service.impl;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Admin;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.mapper.AdminMapper;
import qidian.it.springboot.service.AdminService;


import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

@Service
public class AdminServiceImpl implements AdminService {
    
    @Resource
    private AdminMapper adminMapper;

    // BCrypt密码加密器
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    // 邮箱格式验证正则表达式
    private static final String EMAIL_PATTERN =
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@" +
        "(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";

    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);
    
    @Override
    public Result login(String username, String password) {
        // 参数校验
        if (username == null || username.trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            return Result.fail("密码不能为空");
        }
        
        // 查询管理员
        Admin admin = adminMapper.selectByUsername(username.trim());
        if (Objects.isNull(admin)) {
            return Result.fail("用户名或密码错误");
        }
        
        // BCrypt密码验证
        if (!passwordEncoder.matches(password, admin.getPassword())) {
            return Result.fail("用户名或密码错误");
        }
        
        // 登录成功，返回管理员信息（不包含密码）
        admin.setPassword(null);
        return Result.success("登录成功", admin);
    }
    
    @Override
    public Result register(String username, String password, String email) {
        // 参数校验
        if (username == null || username.trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            return Result.fail("密码不能为空");
        }
        if (email == null || email.trim().isEmpty()) {
            return Result.fail("邮箱不能为空");
        }
        
        // 邮箱格式验证
        if (!isValidEmail(email.trim())) {
            return Result.fail("邮箱格式不正确");
        }
        
        // 检查用户名是否已存在
        Admin existingAdmin = adminMapper.selectByUsername(username.trim());
        if (!Objects.isNull(existingAdmin)) {
            return Result.fail("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        Admin existingEmailAdmin = adminMapper.selectByEmail(email.trim());
        if (!Objects.isNull(existingEmailAdmin)) {
            return Result.fail("邮箱已被注册");
        }
        
        // 创建新管理员
        Admin admin = new Admin();
        admin.setUsername(username.trim());
        admin.setPassword(passwordEncoder.encode(password)); // BCrypt加密存储
        admin.setEmail(email.trim());
        
        // 保存到数据库
        if (adminMapper.insertSelective(admin) > 0) {
            return Result.success("注册成功");
        } else {
            return Result.fail("服务器繁忙，请稍后重试");
        }
    }
    
    @Override
    public Result verifyUsernameAndEmail(String username, String email) {
        // 参数校验
        if (username == null || username.trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }
        if (email == null || email.trim().isEmpty()) {
            return Result.fail("邮箱不能为空");
        }
        
        // 验证用户名和邮箱是否匹配
        Admin admin = adminMapper.selectByUsernameAndEmail(username.trim(), email.trim());
        if (Objects.isNull(admin)) {
            return Result.fail("用户名和邮箱不匹配");
        }
        
        return Result.success("验证成功，可以重设密码");
    }
    
    @Override
    public Result resetPassword(String username, String newPassword) {
        // 参数校验
        if (username == null || username.trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }
        if (newPassword == null || newPassword.trim().isEmpty()) {
            return Result.fail("新密码不能为空");
        }
        
        // 查询管理员
        Admin admin = adminMapper.selectByUsername(username.trim());
        if (Objects.isNull(admin)) {
            return Result.fail("用户不存在");
        }
        
        // 更新密码（BCrypt加密）
        admin.setPassword(passwordEncoder.encode(newPassword));
        if (adminMapper.updateByPrimaryKeySelective(admin) > 0) {
            return Result.success("密码重设成功");
        } else {
            return Result.fail("服务器繁忙，请稍后重试");
        }
    }
    


    /**
     * 验证邮箱格式
     * @param email 邮箱
     * @return 是否有效
     */
    private boolean isValidEmail(String email) {
        return pattern.matcher(email).matches();
    }
}
