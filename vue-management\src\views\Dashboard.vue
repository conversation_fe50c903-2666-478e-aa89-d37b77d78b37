<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon employee">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ employeeCount }}</div>
              <div class="stat-label">员工总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon department">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ departmentCount }}</div>
              <div class="stat-label">部门总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ activeEmployeeCount }}</div>
              <div class="stat-label">在职员工</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon inactive">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ inactiveEmployeeCount }}</div>
              <div class="stat-label">离职员工</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>快捷操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="goToAddEmployee">
              <el-icon><Plus /></el-icon>
              添加员工
            </el-button>
            <el-button type="success" @click="goToAddDepartment">
              <el-icon><Plus /></el-icon>
              添加部门
            </el-button>
            <el-button type="info" @click="goToEmployeeList">
              <el-icon><List /></el-icon>
              员工列表
            </el-button>
            <el-button type="warning" @click="goToDepartmentList">
              <el-icon><List /></el-icon>
              部门列表
            </el-button>
            <el-button type="danger" @click="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-button>

          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统信息</span>
          </template>
          <div class="system-info">
            <p><strong>系统名称：</strong>员工管理系统</p>
            <p><strong>版本：</strong>v1.0.0</p>
            <p><strong>最后更新：</strong>{{ new Date().toLocaleDateString() }}</p>
            <p><strong>管理员：</strong>系统管理员</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { get } from '../api/api.js'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 统计数据
const employeeCount = ref(0)
const departmentCount = ref(0)
const activeEmployeeCount = ref(0)
const inactiveEmployeeCount = ref(0)

// 获取统计数据
const getStatistics = async () => {
  try {
    // 获取员工统计
    const employeeResponse = await get('/employee/list', { currentPage: 1, pageSize: 1000 })
    if (employeeResponse.code === 200) {
      const employees = employeeResponse.data
      employeeCount.value = employees.length
      activeEmployeeCount.value = employees.filter(emp => emp.status === 1).length
      inactiveEmployeeCount.value = employees.filter(emp => emp.status === 0).length
    }

    // 获取部门统计
    const departmentResponse = await get('/department/all')
    if (departmentResponse.code === 200) {
      departmentCount.value = departmentResponse.data.length
    }
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

// 快捷操作导航
const goToAddEmployee = () => {
  router.push('/employee/list')
}

const goToAddDepartment = () => {
  router.push('/department/list')
}

const goToEmployeeList = () => {
  router.push('/employee/list')
}

const goToDepartmentList = () => {
  router.push('/department/list')
}

const logout = () => {
  localStorage.removeItem('admin')
  ElMessage.success('已退出登录')
  router.replace('/login')
}

// 页面加载时获取数据
onMounted(() => {
  getStatistics()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stat-icon.employee {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.department {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.inactive {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.quick-actions .el-button {
  margin: 0;
}

.system-info p {
  margin: 10px 0;
  color: #606266;
}
</style>
