server:
  port: 8080

spring:
  application:
    name: springboot
  datasource:
    username: root
    password: qwer2690682044
    url: **************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-timeout: 10000
      maximum-pool-size: 5
      initialization-fail-timeout: 0

mybatis:
  # 指定 mapper.xml 的位置
  mapper-locations: classpath:mapping/*.xml
  # 扫描实体类包，使用全限定包名
  type-aliases-package: qidian.it.springboot.entity
  configuration:
    #默认开启驼峰命名法，可以不用设置该属性
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


