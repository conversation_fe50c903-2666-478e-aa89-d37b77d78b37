<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springboot.mapper.EmployeeMapper" >
  <resultMap id="BaseResultMap" type="qidian.it.springboot.entity.Employee" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="gender" property="gender" jdbcType="VARCHAR" />
    <result column="age" property="age" jdbcType="INTEGER" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="department_id" property="departmentId" jdbcType="BIGINT" />
    <result column="position" property="position" jdbcType="VARCHAR" />
    <result column="hire_date" property="hireDate" jdbcType="DATE" />
    <result column="status" property="status" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, name, gender, age, phone, email, department_id, position, hire_date, status
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from employee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from employee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="qidian.it.springboot.entity.Employee" >
    insert into employee (id, name, gender, 
      age, phone, email, 
      department_id, position, hire_date, 
      status)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{gender,jdbcType=VARCHAR}, 
      #{age,jdbcType=INTEGER}, #{phone,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{departmentId,jdbcType=BIGINT}, #{position,jdbcType=VARCHAR}, #{hireDate,jdbcType=DATE}, 
      #{status,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="qidian.it.springboot.entity.Employee" >
    insert into employee
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="gender != null" >
        gender,
      </if>
      <if test="age != null" >
        age,
      </if>
      <if test="phone != null" >
        phone,
      </if>
      <if test="email != null" >
        email,
      </if>
      <if test="departmentId != null" >
        department_id,
      </if>
      <if test="position != null" >
        position,
      </if>
      <if test="hireDate != null" >
        hire_date,
      </if>
      <if test="status != null" >
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null" >
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="age != null" >
        #{age,jdbcType=INTEGER},
      </if>
      <if test="phone != null" >
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null" >
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="position != null" >
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="hireDate != null" >
        #{hireDate,jdbcType=DATE},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="qidian.it.springboot.entity.Employee" >
    update employee
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null" >
        gender = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="age != null" >
        age = #{age,jdbcType=INTEGER},
      </if>
      <if test="phone != null" >
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null" >
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="position != null" >
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="hireDate != null" >
        hire_date = #{hireDate,jdbcType=DATE},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="qidian.it.springboot.entity.Employee" >
    update employee
    set name = #{name,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=VARCHAR},
      age = #{age,jdbcType=INTEGER},
      phone = #{phone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      department_id = #{departmentId,jdbcType=BIGINT},
      position = #{position,jdbcType=VARCHAR},
      hire_date = #{hireDate,jdbcType=DATE},
      status = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 新增的查询方法 -->
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from employee
    order by id desc
  </select>

  <select id="selectByDepartmentId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from employee
    where department_id = #{departmentId,jdbcType=BIGINT}
    order by id desc
  </select>

  <select id="countByDepartmentId" resultType="int" parameterType="java.lang.Long">
    select count(*)
    from employee
    where department_id = #{departmentId,jdbcType=BIGINT}
  </select>

  <delete id="deleteBatch" parameterType="java.util.List">
    delete from employee
    where id in
    <foreach collection="list" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="selectByNameLike" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from employee
    where name like concat('%', #{name,jdbcType=VARCHAR}, '%')
    order by id desc
  </select>

  <!-- 新增查询方法：支持联系方式和邮箱唯一性检验 -->
  <select id="selectByPhone" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from employee
    where phone = #{phone,jdbcType=VARCHAR}
  </select>

  <select id="selectByEmail" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from employee
    where email = #{email,jdbcType=VARCHAR}
  </select>
</mapper>