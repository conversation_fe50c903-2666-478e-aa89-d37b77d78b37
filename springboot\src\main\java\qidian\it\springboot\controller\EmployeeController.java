package qidian.it.springboot.controller;

import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.EmployeeServiceImpl;

import javax.annotation.Resource;
import java.util.List;

@RestController
@CrossOrigin // Enable CORS for frontend integration
@RequestMapping("/employee")
public class EmployeeController {

    @Resource
    private EmployeeServiceImpl employeeService;

    // Get employee list with pagination support
    @RequestMapping("/list")
    public Result getEmployeeList(@RequestParam(defaultValue = "1") Integer currentPage,
                                 @RequestParam(defaultValue = "10") Integer pageSize) {
        return employeeService.getEmployeeList(currentPage, pageSize);
    }

    // Create new employee record
    @RequestMapping("/add")
    public Result addEmployee(Employee employee) {
        return employeeService.addEmployee(employee);
    }
    
    // Retrieve employee details by ID
    @RequestMapping("/get")
    public Result getEmployeeById(@RequestParam Long id) {
        return employeeService.getEmployeeById(id);
    }

    // Update existing employee information
    @RequestMapping("/update")
    public Result updateEmployee(Employee employee) {
        return employeeService.updateEmployee(employee);
    }

    // Remove employee record
    @RequestMapping("/delete")
    public Result deleteEmployee(@RequestParam Long id) {
        return employeeService.deleteEmployee(id);
    }

    // Batch delete multiple employees
    @RequestMapping("/deleteBatch")
    public Result deleteEmployeeBatch(@RequestBody List<Long> ids) {
        return employeeService.deleteEmployeeBatch(ids);
    }
    
    /**
     * 根据部门ID查询员工
     * @param departmentId 部门ID
     * @return 员工列表
     */
    @RequestMapping("/getByDepartment")
    public Result getEmployeesByDepartment(@RequestParam Long departmentId) {
        return employeeService.getEmployeesByDepartment(departmentId);
    }
    
    /**
     * 根据姓名模糊查询员工
     * @param name 姓名关键字
     * @return 员工列表
     */
    @RequestMapping("/search")
    public Result searchEmployeesByName(@RequestParam String name) {
        return employeeService.searchEmployeesByName(name);
    }
}
