package qidian.it.springboot.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.mapper.DepartmentMapper;
import qidian.it.springboot.mapper.EmployeeMapper;
import qidian.it.springboot.service.DepartmentService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class DepartmentServiceImpl implements DepartmentService {
    
    @Resource
    private DepartmentMapper departmentMapper;
    
    @Resource
    private EmployeeMapper employeeMapper;
    
    @Override
    public Result getDepartmentList(Integer currentPage, Integer pageSize) {
        // 设置默认值
        if (currentPage == null || currentPage < 1) {
            currentPage = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        
        // 分页查询
        PageHelper.startPage(currentPage, pageSize);
        List<Department> departmentList = departmentMapper.selectAll();
        
        // 封装分页信息
        PageInfo<Department> pageInfo = new PageInfo<>(departmentList);

        return Result.successWithTotal(pageInfo.getList(), (int) pageInfo.getTotal());
    }
    
    @Override
    public Result getAllDepartments() {
        List<Department> departmentList = departmentMapper.selectAll();
        return Result.success(departmentList);
    }
    
    @Override
    public Result addDepartment(Department department) {
        // Input validation
        if (department == null) {
            return Result.fail("Department information cannot be empty");
        }
        if (department.getName() == null || department.getName().trim().isEmpty()) {
            return Result.fail("Department name is required");
        }

        // Check if department name already exists
        Department existingDepartment = departmentMapper.selectByName(department.getName().trim());
        if (!Objects.isNull(existingDepartment)) {
            return Result.fail("Department name already exists");
        }

        // Save department record
        if (departmentMapper.insertSelective(department) > 0) {
            return Result.success("Department created successfully");
        } else {
            return Result.fail("Operation failed, please try again");
        }
    }
    
    @Override
    public Result getDepartmentById(Long id) {
        if (id == null) {
            return Result.fail("部门ID不能为空");
        }
        
        Department department = departmentMapper.selectByPrimaryKey(id);
        if (Objects.isNull(department)) {
            return Result.fail("部门不存在");
        }
        
        return Result.success(department);
    }
    
    @Override
    public Result updateDepartment(Department department) {
        // 参数校验
        if (department == null || department.getId() == null) {
            return Result.fail("部门ID不能为空");
        }
        
        // 检查部门是否存在
        Department existingDepartment = departmentMapper.selectByPrimaryKey(department.getId());
        if (Objects.isNull(existingDepartment)) {
            return Result.fail("部门不存在");
        }
        
        // 如果修改了部门名称，检查新名称是否已存在
        if (department.getName() != null && !department.getName().trim().isEmpty()) {
            Department nameCheckDepartment = departmentMapper.selectByName(department.getName().trim());
            if (!Objects.isNull(nameCheckDepartment) && !nameCheckDepartment.getId().equals(department.getId())) {
                return Result.fail("部门名称已存在");
            }
        }
        
        // 更新部门信息
        if (departmentMapper.updateByPrimaryKeySelective(department) > 0) {
            return Result.success("部门信息更新成功");
        } else {
            return Result.fail("服务器繁忙，请稍后重试");
        }
    }
    
    @Override
    public Result deleteDepartment(Long id) {
        if (id == null) {
            return Result.fail("部门ID不能为空");
        }
        
        // 检查部门是否存在
        Department department = departmentMapper.selectByPrimaryKey(id);
        if (Objects.isNull(department)) {
            return Result.fail("部门不存在");
        }
        
        // 检查该部门下是否有员工
        int employeeCount = employeeMapper.countByDepartmentId(id);
        if (employeeCount > 0) {
            return Result.fail("该部门下存在员工，无法删除");
        }
        
        // 删除部门
        if (departmentMapper.deleteByPrimaryKey(id) > 0) {
            return Result.success("部门删除成功");
        } else {
            return Result.fail("服务器繁忙，请稍后重试");
        }
    }
    
    @Override
    public Result searchDepartmentsByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return Result.fail("搜索关键字不能为空");
        }
        
        List<Department> departments = departmentMapper.selectByNameLike(name.trim());
        return Result.success(departments);
    }
}
