package qidian.it.springboot.controller;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.AdminServiceImpl;

import javax.annotation.Resource;

@RestController
@CrossOrigin // Allow cross-origin requests
@RequestMapping("/admin")
public class AdminController {

    @Resource
    private AdminServiceImpl adminService;

    // Administrator login endpoint
    @RequestMapping("/login")
    public Result login(@RequestParam String username, @RequestParam String password) {
        return adminService.login(username, password);
    }

    // Administrator registration endpoint
    @RequestMapping("/register")
    public Result register(@RequestParam String username,
                          @RequestParam String password,
                          @RequestParam String email) {
        return adminService.register(username, password, email);
    }
    
    /**
     * 忘记密码 - 验证用户名和邮箱
     * @param username 用户名
     * @param email 邮箱
     * @return 验证结果
     */
    @RequestMapping("/verifyAccount")
    public Result verifyAccount(@RequestParam String username, @RequestParam String email) {
        return adminService.verifyUsernameAndEmail(username, email);
    }
    
    /**
     * 重设密码
     * @param username 用户名
     * @param newPassword 新密码
     * @return 重设结果
     */
    @RequestMapping("/resetPassword")
    public Result resetPassword(@RequestParam String username, @RequestParam String newPassword) {
        return adminService.resetPassword(username, newPassword);
    }

}
