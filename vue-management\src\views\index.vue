<template>
  <div class="common-layout">
    <el-container>
      <el-container>
        <el-aside class="aside">
		<Aside />
	</el-aside>
        <el-main class="main">
            <RouterView v-if="$route.path !== '/'" />
            <Dashboard v-else />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
	import { RouterView } from 'vue-router';
import Aside from '../components/Aside.vue'
import Dashboard from './Dashboard.vue'
</script>

<style>
	body{
		margin: 0;
		padding: 0;
         overflow: hidden;
	}
	.header{
		height: 100px;
	}
	.aside{
		height: 100vh;
		width: 14%;
		background-color: rgb(84, 92, 100);
	}
	
	.main{
		height: 90vh;
		width: 86%;
		padding: 0;
	}
</style>