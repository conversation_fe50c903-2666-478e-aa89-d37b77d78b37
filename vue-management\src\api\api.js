import axios from 'axios';

// 创建一个Axios实例
const apiClient = axios.create({
  baseURL: 'http://localhost:8080',
});

// 简单的请求拦截器：自动携带本地登录态（如有）
apiClient.interceptors.request.use((config)=>{
  const admin = localStorage.getItem('admin');
  if(admin){
    try{
      const { username } = JSON.parse(admin);
      // 由于后端当前未实现token校验，这里仅演示附加一个自定义头方便后端扩展
      config.headers['X-Admin-User'] = username || '';
    }catch(e){}
  }
  return config;
});


// 封装GET请求，参数通过查询字符串传递
export const get = async (url, params = {}) => {
  try {
    const response = await apiClient.get(url, { params }); // 传递参数
    return response.data; // 返回响应的数据
  } catch (error) {
    console.error('GET请求失败:', error);
    throw new Error(`GET请求失败: ${error.message}`);
  }
};

// POST请求
export const post = async (url, data, config = {}) => {
  try {
    const response = await apiClient.post(url, data, config);
    return response.data; // 返回数据
  } catch (error) {
    console.error('POST请求失败:', error);
    throw new Error(`POST请求错误: ${error.message}`);
  }
};

// PUT请求
export const put = async (url, data, config = {}) => {
  try {
    const response = await apiClient.put(url, data, config);
    return response.data; // 返回数据
  } catch (error) {
    console.error('PUT请求失败:', error);
    throw new Error(`PUT请求错误: ${error.message}`);
  }
};

// DELETE请求
export const del = async (url, config = {}) => {
  try {
    const response = await apiClient.delete(url, config);
    return response.data; // 返回数据
  } catch (error) {
    console.error('DELETE请求失败:', error);
    throw new Error(`DELETE请求错误: ${error.message}`);
  }
};

export default apiClient; // 导出Axios实例