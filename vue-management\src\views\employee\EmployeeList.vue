<template>
  <div class="employee-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>Staff Management</span>
          <el-button type="success" @click="showAddDialog">New Employee</el-button>
        </div>
      </template>

      <!-- Search Section -->
      <div class="search-bar">
        <el-input
          v-model="searchName"
          placeholder="Enter employee name"
          style="width: 220px; margin-right: 12px;"
          clearable
        />
        <el-button type="primary" @click="searchEmployees">Search</el-button>
        <el-button @click="resetSearch">Clear</el-button>
      </div>

      <!-- Employee Data Table -->
      <el-table :data="employeeList" style="width: 100%" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="Name" width="120" />
        <el-table-column prop="gender" label="Gender" width="80" />
        <el-table-column prop="age" label="Age" width="80" />
        <el-table-column prop="phone" label="Phone" width="130" />
        <el-table-column prop="email" label="Email" width="180" />
        <el-table-column prop="departmentId" label="Dept ID" width="100" />
        <el-table-column prop="position" label="Position" width="120" />
        <el-table-column prop="hireDate" label="Hire Date" width="120" />
        <el-table-column prop="status" label="Status" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? 'Active' : 'Inactive' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="Actions" width="180">
          <template #default="scope">
            <el-button size="small" type="primary" @click="editEmployee(scope.row)">Edit</el-button>
            <el-button size="small" type="danger" @click="deleteEmployee(scope.row.id)">Remove</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Employee Form Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? 'Edit Employee' : 'Add New Employee'"
      width="650px"
    >
      <el-form :model="employeeForm" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="Full Name" prop="name">
          <el-input v-model="employeeForm.name" />
        </el-form-item>
        <el-form-item label="Gender">
          <el-select v-model="employeeForm.gender" placeholder="Select gender">
            <el-option label="Male" value="Male" />
            <el-option label="Female" value="Female" />
          </el-select>
        </el-form-item>
        <el-form-item label="Age">
          <el-input-number v-model="employeeForm.age" :min="18" :max="70" />
        </el-form-item>
        <el-form-item label="Phone Number" prop="phone">
          <el-input v-model="employeeForm.phone" />
        </el-form-item>
        <el-form-item label="Email Address" prop="email">
          <el-input v-model="employeeForm.email" type="email" />
        </el-form-item>
        <el-form-item label="Department">
          <el-select v-model="employeeForm.departmentId" placeholder="Select department">
            <el-option
              v-for="dept in departmentList"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Position">
          <el-input v-model="employeeForm.position" />
        </el-form-item>
        <el-form-item label="Hire Date">
          <el-date-picker
            v-model="employeeForm.hireDate"
            type="date"
            placeholder="Select date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="Status">
          <el-select v-model="employeeForm.status" placeholder="Select status">
            <el-option label="Active" :value="1" />
            <el-option label="Inactive" :value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="saveEmployee">Save</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { get, post } from '../../api/api.js'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const employeeList = ref([])
const departmentList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchName = ref('')
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 员工表单数据
const employeeForm = reactive({
  id: null,
  name: '',
  gender: 'Male',
  age: null,
  phone: '',
  email: '',
  departmentId: null,
  position: '',
  hireDate: '',
  status: 1
})

// Form validation rules - only name is required based on database schema
const rules = {
  name: [{ required: true, message: 'Please enter employee name', trigger: 'blur' }],
  phone: [
    {
      pattern: /^\d{11}$/,
      message: 'Phone number must be 11 digits',
      trigger: 'blur'
    }
  ],
  email: [
    { type: 'email', message: 'Please enter valid email format', trigger: 'blur' }
  ]
}

// 获取员工列表
const getEmployeeList = async () => {
  loading.value = true
  try {
    const response = await get('/employee/list', {
      currentPage: currentPage.value,
      pageSize: pageSize.value
    })
    console.log('员工列表响应:', response) // 添加调试日志
    if (response.code === 200) {
      employeeList.value = response.data || []
      total.value = response.total ?? 0
    } else {
      ElMessage.error(response.message || '获取员工列表失败')
      console.error('获取员工列表失败:', response)
    }
  } catch (error) {
    ElMessage.error('获取员工列表失败: ' + error.message)
    console.error('获取员工列表异常:', error)
  } finally {
    loading.value = false
  }
}

// 获取部门列表
const getDepartmentList = async () => {
  try {
    const response = await get('/department/all')
    if (response.code === 200) {
      departmentList.value = response.data
    }
  } catch (error) {
    ElMessage.error('获取部门列表失败')
  }
}

// 搜索员工
const searchEmployees = async () => {
  if (!searchName.value.trim()) {
    ElMessage.warning('请输入搜索关键字')
    return
  }
  
  loading.value = true
  try {
    const response = await get('/employee/search', { name: searchName.value })
    if (response.code === 200) {
      employeeList.value = response.data
      total.value = response.data.length
    } else {
      ElMessage.error(response.message || '搜索失败')
    }
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchName.value = ''
  currentPage.value = 1
  getEmployeeList()
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑员工
const editEmployee = (employee) => {
  isEdit.value = true
  Object.assign(employeeForm, employee)
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(employeeForm, {
    id: null,
    name: '',
    gender: '男',
    age: null,
    phone: '',
    email: '',
    departmentId: null,
    position: '',
    hireDate: '',
    status: 1
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 保存员工
const saveEmployee = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const url = isEdit.value ? '/employee/update' : '/employee/add'
        // 清理空字符串字段，避免后端接收空字符串导致400
        const payload = Object.fromEntries(Object.entries(employeeForm).filter(([k,v]) => v !== ''))
        const response = await get(url, payload)
        
        if (response.code === 200) {
          ElMessage.success(response.message || '操作成功')
          dialogVisible.value = false
          getEmployeeList()
        } else {
          ElMessage.error(response.message || '操作失败')
        }
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
  })
}

// 删除员工
const deleteEmployee = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这个员工吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await get('/employee/delete', { id })
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getEmployeeList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getEmployeeList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getEmployeeList()
}

// 页面加载时获取数据
onMounted(() => {
  getEmployeeList()
  getDepartmentList()
})
</script>

<style scoped>
.employee-list {
  padding: 24px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 18px;
}

.search-bar {
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination {
  margin-top: 24px;
  text-align: center;
  padding: 16px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}
</style>
