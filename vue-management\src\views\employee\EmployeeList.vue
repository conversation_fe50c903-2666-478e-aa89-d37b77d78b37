<template>
  <div class="employee-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>员工管理</span>
          <el-button type="primary" @click="showAddDialog">添加员工</el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchName"
          placeholder="请输入员工姓名"
          style="width: 200px; margin-right: 10px;"
          clearable
        />
        <el-button type="primary" @click="searchEmployees">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>

      <!-- 员工表格 -->
      <el-table :data="employeeList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="gender" label="性别" width="80" />
        <el-table-column prop="age" label="年龄" width="80" />
        <el-table-column prop="phone" label="联系方式" width="130" />
        <el-table-column prop="email" label="邮箱" width="180" />
        <el-table-column prop="departmentId" label="部门ID" width="100" />
        <el-table-column prop="position" label="职位" width="120" />
        <el-table-column prop="hireDate" label="入职日期" width="120" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '在职' : '离职' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="editEmployee(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteEmployee(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑员工对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑员工' : '添加员工'"
      width="600px"
    >
      <el-form :model="employeeForm" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="employeeForm.name" />
        </el-form-item>
        <el-form-item label="性别">
          <el-select v-model="employeeForm.gender" placeholder="请选择性别">
            <el-option label="男" value="男" />
            <el-option label="女" value="女" />
          </el-select>
        </el-form-item>
        <el-form-item label="年龄">
          <el-input-number v-model="employeeForm.age" :min="18" :max="65" />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input v-model="employeeForm.phone" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="employeeForm.email" type="email" />
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="employeeForm.departmentId" placeholder="请选择部门">
            <el-option
              v-for="dept in departmentList"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="职位">
          <el-input v-model="employeeForm.position" />
        </el-form-item>
        <el-form-item label="入职日期">
          <el-date-picker
            v-model="employeeForm.hireDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="employeeForm.status" placeholder="请选择状态">
            <el-option label="在职" :value="1" />
            <el-option label="离职" :value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEmployee">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { get, post } from '../../api/api.js'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const employeeList = ref([])
const departmentList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchName = ref('')
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 员工表单数据
const employeeForm = reactive({
  id: null,
  name: '',
  gender: '男',
  age: null,
  phone: '',
  email: '',
  departmentId: null,
  position: '',
  hireDate: '',
  status: 1
})

// 表单验证规则（根据数据库结构，只有name是必填的）
const rules = {
  name: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 获取员工列表
const getEmployeeList = async () => {
  loading.value = true
  try {
    const response = await get('/employee/list', {
      currentPage: currentPage.value,
      pageSize: pageSize.value
    })
    console.log('员工列表响应:', response) // 添加调试日志
    if (response.code === 200) {
      employeeList.value = response.data || []
      total.value = response.total ?? 0
    } else {
      ElMessage.error(response.message || '获取员工列表失败')
      console.error('获取员工列表失败:', response)
    }
  } catch (error) {
    ElMessage.error('获取员工列表失败: ' + error.message)
    console.error('获取员工列表异常:', error)
  } finally {
    loading.value = false
  }
}

// 获取部门列表
const getDepartmentList = async () => {
  try {
    const response = await get('/department/all')
    if (response.code === 200) {
      departmentList.value = response.data
    }
  } catch (error) {
    ElMessage.error('获取部门列表失败')
  }
}

// 搜索员工
const searchEmployees = async () => {
  if (!searchName.value.trim()) {
    ElMessage.warning('请输入搜索关键字')
    return
  }
  
  loading.value = true
  try {
    const response = await get('/employee/search', { name: searchName.value })
    if (response.code === 200) {
      employeeList.value = response.data
      total.value = response.data.length
    } else {
      ElMessage.error(response.message || '搜索失败')
    }
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchName.value = ''
  currentPage.value = 1
  getEmployeeList()
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑员工
const editEmployee = (employee) => {
  isEdit.value = true
  Object.assign(employeeForm, employee)
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(employeeForm, {
    id: null,
    name: '',
    gender: '男',
    age: null,
    phone: '',
    email: '',
    departmentId: null,
    position: '',
    hireDate: '',
    status: 1
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 保存员工
const saveEmployee = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const url = isEdit.value ? '/employee/update' : '/employee/add'
        // 清理空字符串字段，避免后端接收空字符串导致400
        const payload = Object.fromEntries(Object.entries(employeeForm).filter(([k,v]) => v !== ''))
        const response = await get(url, payload)
        
        if (response.code === 200) {
          ElMessage.success(response.message || '操作成功')
          dialogVisible.value = false
          getEmployeeList()
        } else {
          ElMessage.error(response.message || '操作失败')
        }
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
  })
}

// 删除员工
const deleteEmployee = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这个员工吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await get('/employee/delete', { id })
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getEmployeeList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getEmployeeList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getEmployeeList()
}

// 页面加载时获取数据
onMounted(() => {
  getEmployeeList()
  getDepartmentList()
})
</script>

<style scoped>
.employee-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
